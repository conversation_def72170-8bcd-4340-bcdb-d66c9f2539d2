2025-08-05 13:13:57,069 - __main__ - INFO - Laboratory automation framework initialized
2025-08-05 13:13:57,069 - instruments.base - INFO - Mock connection established to MOCK::KEITHLEY2400::INSTR
2025-08-05 13:13:57,069 - instruments.base - INFO - Initialized instrument: Demo K2400
2025-08-05 13:13:57,069 - instruments.keithley2400 - INFO - Initialized Demo K2400: KEITHLEY INSTRUMENTS INC.,MODEL 2400,1234567,C30 Mar 17 1999 09:32:39
2025-08-05 13:13:57,069 - __main__ - INFO - Added instrument: smu1
2025-08-05 13:13:57,070 - instruments.keithley2400 - INFO - Configured Demo K2400 as voltage source: 2.0V, 0.01A limit
2025-08-05 13:13:57,171 - instruments.base - INFO - Mock connection closed to MOCK::KEITHLEY2400::INSTR
2025-08-05 13:13:57,173 - __main__ - INFO - Closed instrument: smu1
2025-08-05 13:14:13,079 - __main__ - INFO - Laboratory automation framework initialized
2025-08-05 13:14:13,079 - instruments.base - INFO - Mock connection established to MOCK::KEITHLEY2400::INSTR
2025-08-05 13:14:13,080 - instruments.base - INFO - Initialized instrument: IV Sweep K2400
2025-08-05 13:14:13,080 - instruments.keithley2400 - INFO - Initialized IV Sweep K2400: KEITHLEY INSTRUMENTS INC.,MODEL 2400,1234567,C30 Mar 17 1999 09:32:39
2025-08-05 13:14:13,080 - __main__ - INFO - Added instrument: smu1
2025-08-05 13:14:13,080 - instruments.keithley2400 - INFO - Configured IV Sweep K2400 as voltage source: 0V, 0.01A limit
2025-08-05 13:14:13,643 - instruments.base - INFO - Mock connection closed to MOCK::KEITHLEY2400::INSTR
2025-08-05 13:14:13,644 - __main__ - INFO - Closed instrument: smu1
2025-08-05 13:29:21,651 - config_manager - INFO - Configuration loaded from config.json
2025-08-05 13:29:21,781 - gui.widgets.plotting - INFO - Real-time plot widget initialized
2025-08-05 13:29:21,825 - gui.widgets.plotting - INFO - Multi-channel plot widget initialized with 3 channels
2025-08-05 13:29:21,868 - instruments.base - ERROR - Failed to connect to GPIB0::24::INSTR: Please install linux-gpib (Linux) or gpib-ctypes (Windows, Linux) to use this resource type. Note that installing gpib-ctypes will give you access to a broader range of functionalities.
No module named 'gpib'
2025-08-05 13:29:21,868 - config_manager - ERROR - Failed to load instrument smu1: Could not connect to GPIB0::24::INSTR: Please install linux-gpib (Linux) or gpib-ctypes (Windows, Linux) to use this resource type. Note that installing gpib-ctypes will give you access to a broader range of functionalities.
No module named 'gpib'
2025-08-05 13:29:21,868 - config_manager - INFO - Skipping disabled instrument: smu2
2025-08-05 13:29:21,868 - instruments.base - INFO - Mock connection established to MOCK::KEITHLEY2400::INSTR
2025-08-05 13:29:21,868 - instruments.base - INFO - Initialized instrument: Mock SMU
2025-08-05 13:29:21,869 - instruments.keithley2400 - INFO - Initialized Mock SMU: KEITHLEY INSTRUMENTS INC.,MODEL 2400,1234567,C30 Mar 17 1999 09:32:39
2025-08-05 13:29:21,869 - config_manager - INFO - Loaded instrument: mock_smu
2025-08-05 13:29:21,872 - gui.widgets.instrument_control - INFO - Created instrument widget for mock_smu
2025-08-05 13:29:21,872 - gui.widgets.instrument_control - INFO - Added instrument mock_smu to panel
2025-08-05 13:53:21,157 - __main__ - INFO - Laboratory automation framework initialized
2025-08-05 13:53:21,157 - instruments.base - INFO - Mock connection established to MOCK::KEITHLEY2400::INSTR
2025-08-05 13:53:21,157 - instruments.base - INFO - Initialized instrument: Demo K2400
2025-08-05 13:53:21,157 - instruments.keithley2400 - INFO - Initialized Demo K2400: KEITHLEY INSTRUMENTS INC.,MODEL 2400,1234567,C30 Mar 17 1999 09:32:39
2025-08-05 13:53:21,158 - __main__ - INFO - Added instrument: smu1
2025-08-05 13:53:21,158 - instruments.keithley2400 - INFO - Configured Demo K2400 as voltage source: 2.0V, 0.01A limit
2025-08-05 13:53:21,259 - instruments.base - INFO - Mock connection closed to MOCK::KEITHLEY2400::INSTR
2025-08-05 13:53:21,259 - __main__ - INFO - Closed instrument: smu1
2025-08-05 13:53:41,389 - __main__ - INFO - Laboratory automation framework initialized
2025-08-05 13:53:41,389 - instruments.base - INFO - Mock connection established to MOCK::KEITHLEY2400::INSTR
2025-08-05 13:53:41,390 - instruments.base - INFO - Initialized instrument: IV Sweep K2400
2025-08-05 13:53:41,390 - instruments.keithley2400 - INFO - Initialized IV Sweep K2400: KEITHLEY INSTRUMENTS INC.,MODEL 2400,1234567,C30 Mar 17 1999 09:32:39
2025-08-05 13:53:41,390 - __main__ - INFO - Added instrument: smu1
2025-08-05 13:53:41,390 - instruments.keithley2400 - INFO - Configured IV Sweep K2400 as voltage source: 0V, 0.01A limit
2025-08-05 13:53:41,950 - instruments.base - INFO - Mock connection closed to MOCK::KEITHLEY2400::INSTR
2025-08-05 13:53:41,950 - __main__ - INFO - Closed instrument: smu1
